using Microsoft.EntityFrameworkCore;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Interfaces;
using RealEstate.Infrastructure;
using RealEstate.Infrastructure.Repositories;
using RealEstate.Infrastructure.Services;
using RealEstate.Infrastructure.Services.Notifications;
using Serilog;

namespace Yezhome.WorkerService
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = Host.CreateApplicationBuilder(args);

            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(builder.Configuration)
                .CreateLogger();

            builder.Services.AddSerilog();

            // Get connection string
            var connectionString = builder.Configuration.GetConnectionString("YezHomeConnection");
            if (connectionString == null)
            {
                throw new InvalidOperationException("Connection string 'YezHomeConnection' not found.");
            }

            // Add DbContext
            builder.Services.AddDbContext<ApplicationDbContext>(options =>
                options.UseNpgsql(connectionString, o => o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery)));

            // Register repositories
            builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
            builder.Services.AddScoped<IUserRepository, UserRepository>();
            builder.Services.AddScoped<IBlogRepository, BlogRepository>();
            builder.Services.AddScoped<IPropertyRepository, PropertyRepository>();
            builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            builder.Services.AddScoped(typeof(IAuditableRepository<>), typeof(AuditableRepository<>));

            // Register notification services
            builder.Services.AddScoped<InAppNotificationService>();
            builder.Services.AddScoped<PushNotificationService>();
            builder.Services.AddScoped<EmailNotificationService>();
            builder.Services.AddScoped<INotificationService, CompositeNotificationService>();
            builder.Services.AddScoped<IEmailSender, AwsSesEmailSender>();

            // Register application services
            builder.Services.AddScoped<IExpirationService, ExpirationService>();

            // Register the background service
            builder.Services.AddHostedService<ExpirationWorker>();

            var host = builder.Build();
            host.Run();
        }
    }
}