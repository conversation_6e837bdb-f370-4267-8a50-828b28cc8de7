using RealEstate.Application.Interfaces;

namespace Yezhome.WorkerService
{
    public class ExpirationWorker : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ExpirationWorker> _logger;

        public ExpirationWorker(IServiceProvider serviceProvider, ILogger<ExpirationWorker> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("ExpirationWorker started at: {time}", DateTimeOffset.Now);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var now = DateTime.Now;
                    var targetTime = new DateTime(now.Year, now.Month, now.Day, 2, 0, 0); // 2:00 AM today

                    // If it's already past 2:00 AM today, schedule for 2:00 AM tomorrow
                    if (now > targetTime)
                    {
                        targetTime = targetTime.AddDays(1);
                    }

                    var delay = targetTime - now;
                    _logger.LogInformation("Next expiration check scheduled for: {targetTime}. Waiting for: {delay}",
                        targetTime, delay);

                    // Wait until the target time
                    await Task.Delay(delay, stoppingToken);

                    if (stoppingToken.IsCancellationRequested)
                        break;

                    // Execute the expiration check
                    await RunExpirationCheck(stoppingToken);

                    // Wait for 1 minute to avoid running multiple times if the service restarts quickly
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("ExpirationWorker was cancelled");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error in ExpirationWorker");
                    // Wait 5 minutes before retrying in case of error
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                }
            }

            _logger.LogInformation("ExpirationWorker stopped at: {time}", DateTimeOffset.Now);
        }

        private async Task RunExpirationCheck(CancellationToken cancellationToken)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var expirationService = scope.ServiceProvider.GetRequiredService<IExpirationService>();

                _logger.LogInformation("Starting property expiration check");
                var result = await expirationService.RunExpirationCheckAsync(cancellationToken);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Property expiration check completed successfully");
                }
                else
                {
                    _logger.LogError("Property expiration check failed: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during property expiration check");
            }
        }
    }
}
