using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IExpirationService
    {
        /// <summary>
        /// Runs the property expiration check process
        /// Checks for properties expiring in 3 days, 1 day, or today
        /// Sends appropriate notifications and updates expired properties
        /// </summary>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>Result indicating success or failure of the operation</returns>
        Task<Result> RunExpirationCheckAsync(CancellationToken cancellationToken = default);
    }
}
