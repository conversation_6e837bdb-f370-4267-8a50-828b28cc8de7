{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information", "EFCore": "Information", "Yezhome.WorkerService": "Information"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "Seq", "Args": {"serverUrl": "http://localhost:5341", "apiKey": "yezhome_worker_service_key"}}]}, "ConnectionStrings": {"YezHomeConnection": "Host=localhost;Port=5432;Database=yezhome;Username=postgres;Password=******;SSL Mode=VerifyFull;Channel Binding=Require;"}, "AWS": {"Region": "us-east-1", "SES": {"AccessKey": "SES_ACCESS_KEY", "SecretKey": "SES_SECRET_KEY", "Region": "us-east-1", "FromEmail": "<EMAIL>", "FromName": "YEZHOME"}}}