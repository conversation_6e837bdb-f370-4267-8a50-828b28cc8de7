{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Error", "Microsoft.EntityFrameworkCore": "Error", "EFCore": "Error", "Yezhome.WorkerService": "Information"}, "Console": {"IncludeScopes": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Error", "System": "Error", "Yezhome.WorkerService": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "Seq", "Args": {"serverUrl": "http://seq:5341", "apiKey": "yezhome_worker_service_key"}}]}}